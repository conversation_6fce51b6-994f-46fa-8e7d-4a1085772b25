.custom-tab-container {
  height: 44px;
  background: linear-gradient(
      rgba(60, 110, 240, 0.05),
      rgba(60, 110, 240, 0.05)
    ),
    linear-gradient(rgba(241, 242, 244, 1), rgba(241, 242, 244, 1));
  border: 2px solid rgba(255, 255, 255, 1);
  border-radius: 12px 12px 0 0;
  display: flex;

  .custom-tab-text {
    line-height: 44px;
    position: relative;
    &.selected {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 600;
      cursor: pointer;
      position: relative;
      color: rgba(35, 37, 43, 1);
      background: linear-gradient(
        180deg,
        rgba(239, 241, 248, 1) 0%,
        rgba(255, 255, 255, 1) 100%
      );
    }
    &.unselected {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 600;
      cursor: pointer;
      color: rgba(134, 141, 159, 1);
    }
    &.first {
      padding-left: 10px;
      margin-right: 10px;
      border-radius: 12px 0 0 0;

      // 只有在选中状态时才显示右侧伪元素
      &.selected::after {
        content: '';
        display: block;
        position: absolute;
        top: -3px;
        width: 48px;
        height: 48px;
        z-index: 0;
        pointer-events: none;
        background: url('@/assets/image/common/tab-right.png') no-repeat 0 /
          contain;
        right: -48px;
      }
    }

    &.non-first {
      margin-left: 30px;

      // 只有在选中状态时才显示左侧和右侧伪元素
      &.selected::before {
        content: '';
        display: block;
        position: absolute;
        top: -3px;
        width: 48px;
        height: 48px;
        z-index: 0;
        pointer-events: none;
        background: url('@/assets/image/common/tab-left.png') no-repeat 0 /
          contain;
        left: -48px;
      }

      &.selected::after {
        content: '';
        display: block;
        position: absolute;
        top: -3px;
        width: 48px;
        height: 48px;
        z-index: 0;
        pointer-events: none;
        background: url('@/assets/image/common/tab-right.png') no-repeat 0 /
          contain;
        right: -48px;
      }
    }
    .tab-label {
      position: relative;
      display: inline-block;
      padding: 0 16px;
    }
  }
  .count-badge {
    position: absolute;
    top: -3px;
    right: -3px;
    background-color: #fb4c8d;
    border: 2px solid white;
    color: white;
    font-size: 12px;
    font-weight: 600;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    z-index: 10;
    height: 22px;
    width: 22px;
    border-radius: 50%;
    white-space: nowrap;
  }
}
