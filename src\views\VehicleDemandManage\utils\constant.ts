import { FormConfig } from '@jd/x-coreui';

// 表格列配置
export const tableColumns: any[] = [
  {
    title: '需求编码',
    dataIndex: 'requirementNumber',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '省市区',
    dataIndex: 'contryName',
    align: 'left',
    width: 150,
  },
  {
    title: '省区片区',
    dataIndex: 'areaName',
    align: 'left',
    width: 120,
  },
  {
    title: '站点名称',
    dataIndex: 'stationName',
    align: 'left',
    width: 120,
  },
  {
    title: '站点编号',
    dataIndex: 'stationNumber',
    align: 'left',
    width: 120,
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    align: 'left',
    width: 200,
  },
  {
    title: '站点用途',
    dataIndex: 'stationUseCaseName',
    align: 'left',
    width: 100,
  },
  {
    title: '车辆型号',
    dataIndex: 'vehicleModelName',
    align: 'left',
    width: 120,
  },
  {
    title: '车辆数',
    dataIndex: 'count',
    align: 'left',
    width: 80,
  },
  {
    title: '提报人',
    dataIndex: 'contact',
    align: 'left',
    width: 100,
  },
  {
    title: '联系方式',
    dataIndex: 'contactPhone',
    align: 'left',
    width: 120,
  },
  {
    title: '备选联系人',
    dataIndex: 'alternateContact',
    align: 'left',
    width: 100,
  },
  {
    title: '备选联系方式',
    dataIndex: 'alternateContactPhone',
    align: 'left',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
    width: 150,
  },
  {
    title: '审核状态',
    dataIndex: 'statusName',
    align: 'left',
    width: 100,
  },
  {
    title: '审批人',
    dataIndex: 'reviewerUser',
    align: 'left',
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'statusModifyTime',
    align: 'left',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    align: 'left',
    fixed: 'right',
  },
];

// 搜索表单配置
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'provinceCityCountry',
      label: '省市区',
      placeholder: '请选择省市区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'id', children: 'children' },
    },
    {
      fieldName: 'provinceAgencyArea',
      label: '省区片区',
      placeholder: '请选择省区片区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'code', children: 'children' },
    },
    {
      fieldName: 'stationNumber',
      label: '站点名称',
      placeholder: '请选择站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
    },
    {
      fieldName: 'vehicleModelType',
      label: '车辆型号',
      placeholder: '请选择车辆型号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
    },
    {
      fieldName: 'stationUseCase',
      label: '站点用途',
      placeholder: '请选择站点用途',
      type: 'select',
      labelInValue: false,
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      type: 'rangeTime',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
  ],
};

// Tab配置
export const tabsConfig = [
  {
    key: '',
    label: '全部',
    statusKey: 'TOTAL',
  },
  {
    key: 'REVIEW',
    label: '审批中',
    statusKey: 'REVIEW',
  },
  {
    key: 'PLACED',
    label: '已下单',
    statusKey: 'PLACED',
  },
  {
    key: 'REJECTED',
    label: '已驳回',
    statusKey: 'REJECTED',
  },
];

// 默认隐藏的列
export const defaultHiddenColumns = ['contryName'];

// 默认固定在左侧的列
export const defaultLeftFixedColumns = ['requirementNumber'];

// 默认固定在右侧的列
export const defaultRightFixedColumns = ['operation'];
