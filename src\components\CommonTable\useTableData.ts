import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { CommonResponse, CommonTableResponse } from './type';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isNullObject } from '@/utils/utils';

export function useTableData<T, U>(
  searchOptions: T,
  fetchData: (options: T) => Promise<CommonResponse<CommonTableResponse<U>>>,
  tableKey?: string | number,
  autoFetch: boolean = true,
): {
  tableData: CommonTableResponse<U> | null;
  loading: boolean;
  reloadTable: () => void;
} {
  const [responseData, setResponseData] =
    useState<CommonTableResponse<U> | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const reloadTable = () => {
    getData();
  };
  const getData = async () => {
    setLoading(true);
    try {
      const res = await fetchData(searchOptions);
      if (res.code === HttpStatusCode.Success) {
        if (!isNullObject(res.data)) {
          setResponseData(res.data);
        } else {
          message.error(res.message);
        }
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (!autoFetch || !searchOptions) {
      return;
    }
    console.log('会进来这里吗');
    getData();
  }, [
    JSON.stringify(searchOptions),
    JSON.stringify(fetchData),
    tableKey,
    autoFetch,
  ]);

  return { tableData: responseData, loading, reloadTable };
}
