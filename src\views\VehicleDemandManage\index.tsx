import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import { message } from 'antd';
import { CommonTable, CommonForm } from '@jd/x-coreui';
import { useTableData } from '@/components/CommonTable/useTableData';
import { TableOperateBtn } from '@/components';
import CustomTabs from '@/components/CustomTabs';
import {
  searchConfig,
  tableColumns,
  tabsConfig,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
} from './utils/constant';
import { VehicleDemandManageApi, CommonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  sortColumnsByState,
  createDefaultColumnsState,
} from '@jd/x-coreui/es/components/CommonTable/columnUtils';
import { formatDateToSecond } from '@/utils/utils';
import './index.scss';

const VehicleDemandManage = () => {
  const fetchApi = VehicleDemandManageApi;
  const searchFormRef = useRef<any>(null);
  const searchFormDomRef = useRef<any>(null);

  // 初始搜索条件 - 平铺结构
  const initSearchCondition = {
    stationNumber: '',
    vehicleModelType: '',
    stationUseCase: '',
    startTime: '',
    endTime: '',
    provinceId: '',
    cityId: '',
    countryId: '',
    provinceAgencyCode: '',
    areaCode: '',
    status: '',
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] = useState(initSearchCondition);
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  const [countMap, setCountMap] = useState({
    TOTAL: 0,
    REVIEW: 0,
    PLACED: 0,
    REJECTED: 0,
  });
  const [selectInfo, setSelectInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: any;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(searchConfig);
  const defaultColumnsState = useMemo(() => {
    return createDefaultColumnsState(
      tableColumns,
      defaultHiddenColumns,
      defaultLeftFixedColumns,
      defaultRightFixedColumns,
    );
  }, []);
  const [columnsState, setColumnsState] = useState<any>(defaultColumnsState);

  // 使用useCallback稳定fetchData函数引用
  const fetchDataCallback = useCallback(
    (params: any) => fetchApi.getVehicleDemandPage(params),
    [],
  );

  const { tableData, loading, reloadTable } = useTableData(
    {...searchCondition},
    fetchDataCallback,
  );

  // 添加调试信息，监听searchCondition的变化
  useEffect(() => {
    console.log('searchCondition发生变化：', searchCondition);
  }, [searchCondition]);

  useEffect(() => {
    initializeDropdownData();
    // 不需要手动调用handleSearch，useTableData会自动使用初始的searchCondition
  }, []);

  // 更新统计数据
  useEffect(() => {
    if ((tableData as any)?.countMap) {
      setCountMap((tableData as any).countMap);
    }
  }, [tableData]);

  const initializeDropdownData = async () => {
    try {
      const [
        provinceCityRes,
        provinceAgencyRes,
        stationRes,
        vehicleModelRes,
        stationUseCaseRes,
      ] = await Promise.all([
        fetchApi.getProvinceCityCountryList(),
        fetchApi.getProvinceAgencyAreaList(),
        fetchApi.getStationList(),
        fetchApi.getVehicleModelList(),
        new CommonApi().getCommonDropDown({ keyList: ['STATION_USE_CASE'] }),
      ]);

      // 更新搜索表单配置
      setSearchFormConfig({
        ...searchConfig,
        fields: searchConfig.fields.map((field) => {
          if (field.fieldName === 'provinceCityCountry') {
            return {
              ...field,
              options:
                provinceCityRes.code === '0000' ? provinceCityRes.data : [],
            };
          }
          if (field.fieldName === 'provinceAgencyArea') {
            return {
              ...field,
              options:
                provinceAgencyRes.code === '0000' ? provinceAgencyRes.data : [],
            };
          }
          if (field.fieldName === 'stationNumber') {
            return {
              ...field,
              options:
                stationRes.code === '0000'
                  ? stationRes.data.map((item: any) => ({
                      value: item.stationNumber,
                      label: item.stationName,
                    }))
                  : [],
            };
          }
          if (field.fieldName === 'vehicleModelType') {
            return {
              ...field,
              options:
                vehicleModelRes.code === '0000'
                  ? vehicleModelRes.data.map((item: any) => ({
                      value: item.vehicleModelType,
                      label: item.vehicleModelName,
                    }))
                  : [],
            };
          }
          if (field.fieldName === 'stationUseCase') {
            return {
              ...field,
              options:
                stationUseCaseRes.code === '0000'
                  ? stationUseCaseRes.data.stationUseCaseList?.map(
                      (item: any) => ({
                        value: item.code,
                        label: item.name,
                      }),
                    ) || []
                  : [],
            };
          }
          return field;
        }),
      });
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      message.error('初始化下拉框数据失败');
    }
  };

  const handleSearch = (searchParams: any, extraParams?: any) => {
    const {
      createTime,
      provinceCityCountry,
      provinceAgencyArea,
      ...otherParams
    } = searchParams;
    const formatTime: any =
      createTime?.length > 0 ? formatDateToSecond(createTime) : {};
    const currentPageNum = extraParams?.pageNum;
    const currentPageSize = extraParams?.pageSize;
    const currentTabKey = extraParams?.tabKey;
    const condition = {
      ...searchCondition,
      ...otherParams,
      startTime: formatTime.startTime
        ? `${formatTime.startTime.split(' ')[0]} 00:00:00`
        : '',
      endTime: formatTime.endTime
        ? `${formatTime.endTime.split(' ')[0]} 23:59:59`
        : '',
      provinceId: provinceCityCountry?.[0] || '',
      cityId: provinceCityCountry?.[1] || '',
      countryId: provinceCityCountry?.[2] || '',
      provinceAgencyCode: provinceAgencyArea?.[0] || '',
      areaCode: provinceAgencyArea?.[1] || '',
      status: currentTabKey ? currentTabKey : activeTabKey || '',
      pageNum: currentPageNum ? currentPageNum : searchCondition.pageNum,
      pageSize: currentPageSize ? currentPageSize : searchCondition.pageSize,
    };
    console.log('进入这里！');
    setSearchCondition(condition);
  };

  const onSearchClick = (values: any) => {
    console.log('values', values);
    handleSearch(values, { pageNum: 1 });
    // setSearchCondition({
    //   ...searchCondition,
    //   ...values,
    //   pageNum: 1,
    // });
  };

  const onResetClick = () => {
    handleSearch(initSearchCondition, { pageNum: 1 });
    if (searchFormRef.current) {
      searchFormRef.current.resetFields();
    }
  };

  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    const currentSearchForm = searchFormRef.current?.getFieldsValue() || {};
    handleSearch(currentSearchForm, { pageNum: 1, tabKey: key });
    selectInfo.clearFunc();
    setSelectInfo({
      selectedRowKeys: [],
      selectedRows: [],
      clearFunc: () => {},
    });
  };

  const formatColumns = useMemo(() => {
    return tableColumns.map((col) => {
      if (col?.dataIndex === 'operation') {
        return {
          ...col,
          render: (_: any, record: any) => {
            return (
              <div className="operate">
                <TableOperateBtn
                  title="查看详情"
                  handleClick={() => {
                    console.log('查看详情', record);
                    message.info(`查看详情: ${record.requirementNumber}`);
                  }}
                />
              </div>
            );
          },
        };
      }
      return {
        ...col,
        render: (text: any) => `${text || '-'}`,
      };
    });
  }, []);

  const dynamicColumns = useMemo(() => {
    return sortColumnsByState(formatColumns, columnsState);
  }, [formatColumns, columnsState]);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新增需求',
      key: 'addDemand',
      onClick: () => {
        console.log('新增需求');
        message.info('新增需求功能待开发');
      },
    },
    {
      show: true,
      title: '批量审批',
      key: 'batchApprove',
      onClick: () => {
        if (selectInfo.selectedRowKeys.length === 0) {
          message.error('请至少选择一条数据');
          return;
        }
        console.log('批量审批', selectInfo.selectedRowKeys);
        message.info('批量审批功能待开发');
      },
    },
    {
      show: true,
      title: '导出',
      key: 'export',
      onClick: () => {
        if (selectInfo.selectedRowKeys.length === 0) {
          message.error('请至少选择一条数据进行导出');
          return;
        }
        console.log('导出', selectInfo.selectedRowKeys);
        message.info('导出功能待开发');
      },
    },
  ];

  const formatTabsConfig = useMemo(() => {
    return tabsConfig.map((tab) => ({
      key: tab.key,
      label: tab.label,
      count: countMap[tab.statusKey as keyof typeof countMap] || 0,
    }));
  }, [countMap]);

  return (
    <div className="vehicle-demand-manage">
      {/* 搜索表单 */}
      <div ref={searchFormDomRef}>
        <CommonForm
          formConfig={searchFormConfig}
          defaultValue={searchCondition}
          layout="inline"
          formType="search"
          getFormInstance={(ref) => (searchFormRef.current = ref)}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>

      {/* 自定义Tabs */}
      <div className="table-container">
        <CustomTabs
          items={formatTabsConfig}
          activeKey={activeTabKey}
          onChange={handleTabChange}
        />

        {/* 表格 */}
        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey={'vehicle-demand-manage-table'}
          columns={dynamicColumns}
          loading={loading}
          rowKey="id"
          middleBtns={middleBtns}
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            console.log('此时的分页变化', value);
            setSearchCondition(value);
          }}
          crossPageSelect={(keys: any, rows: any, clearFunc: any) => {
            setSelectInfo({
              selectedRowKeys: keys,
              selectedRows: rows,
              clearFunc: clearFunc,
            });
          }}
          searchRef={searchFormDomRef}
          // 列配置相关属性
          showColumnSetting={true}
          columnsState={{
            value: columnsState,
            onChange: setColumnsState,
            persistenceType: 'localStorage',
          }}
          defaultColumnsState={defaultColumnsState}
        />
      </div>
    </div>
  );
};

export default React.memo(VehicleDemandManage);
